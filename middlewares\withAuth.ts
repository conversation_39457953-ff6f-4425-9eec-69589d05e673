import { hasSupscription } from '@/lib/getDayCount';
import { getToken } from 'next-auth/jwt';
import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';
import { getDayCount, isAuthPath, isPublicPath, isProfile } from './helpers';

export function withAuth(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
    const callBackUrl = `${req.nextUrl.pathname}${req.nextUrl.search}`;

    // Skip auth check for public paths
    if (isPublicPath(req.nextUrl.pathname)) {
      return NextResponse.next();
    }

    // If user is authenticated but trying to access auth pages, redirect to home
    if (token && isAuthPath(req.nextUrl.pathname)) {
      return NextResponse.redirect(new URL(`/`, req.nextUrl));
    }

    // At this point, verify<PERSON><PERSON><PERSON><PERSON><PERSON> has already handled token validation
    // So we know the user has a valid token for protected routes

    if (
      token &&
      req.nextUrl.pathname.includes('email-verify') &&
      !hasSupscription(token)
    ) {
      return NextResponse.redirect(new URL(`/checkout`, req.nextUrl));
    }

    if (
      token &&
      req.nextUrl.pathname.includes('/dashboard/profiles') &&
      isProfile(token.email)
    ) {
      return NextResponse.redirect(
        new URL(`/dashboard/tcfhistory`, req.nextUrl),
      );
    }

    if (req.nextUrl.pathname.includes('methodologie')) {
      let days: number = 0;
      if (req.nextUrl.pathname.includes('TCF')) {
        days =
          token?.role == 'admin'
            ? 100
            : getDayCount(token?.remains?.remainTCF?.remain_day || null);
      }
      if (req.nextUrl.pathname.includes('TEF')) {
        days =
          token?.role == 'admin'
            ? 100
            : getDayCount(token?.remains?.remainTEF?.remain_day || null);
      }

      if (days < 1) {
        return NextResponse.redirect(
          new URL(
            `/checkout?callbackUrl=${encodeURIComponent(callBackUrl)}`,
            req.nextUrl,
          ),
        );
      }
    }

    if (
      token &&
      req.nextUrl.pathname.includes('administration') &&
      token.role !== 'admin'
    ) {
      return NextResponse.redirect(new URL(`/`, req.nextUrl));
    }

    return middleware(req, event);
  };
}
