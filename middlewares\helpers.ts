import { BASEURL } from '@/config';
import logger from '@/lib/logger';

export function isPublicPath(pathname: string): Boolean {
  const PUBLIC_PATHS = [
    '/',
    '/about',
    '/contact',
    '/politique-confidentialite',
    '/condition-remboursement',
    '/faq',
    '/maintenance',
    '/checkout',
    '/checkout-dealer',
    '/news/tcf',
    '/news/tef',
    '/auth',
    '/signin',
    '/signup',
    '/reset-password',
  ];
  return PUBLIC_PATHS.includes(pathname);
}

export function isAuthPath(pathname: string): Boolean {
  const AUTH_PATHS = ['/auth', '/signin', '/signup', '/reset-password'];
  return AUTH_PATHS.includes(pathname);
}

export const getDayCount = (endDate: string | null) => {
  if (!endDate) return 0;
  const start = new Date(Date.now());
  const end = new Date(endDate);
  const difference = end.getTime() - start.getTime();
  const days = Math.ceil(difference / (1000 * 3600 * 24));
  return days > 0 ? days : 0;
};

export function isProfile(email: string | undefined | null): boolean {
  if (!email || typeof email !== 'string') return false;

  const parts = email.split('/');
  if (parts.length !== 2) return false;

  const [dealerEmail] = parts;

  // Expression régulière basique pour un email valide
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  return emailRegex.test(dealerEmail);
}

export const getFreeSerrie = async (type: 'tef' | 'tcf') => {
  const response = await fetch(`${BASEURL}/api/free_serie/${type}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
    },
  });
  if (!response.ok) {
    logger.error('Error fetching free series', await response.json());
    return null;
  }
  const data = await response.json();
  return data;
};
