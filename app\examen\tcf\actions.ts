'use server';
import { AuthService } from '@/services/auth';
import { redirect } from 'next/navigation';
import { z } from 'zod';
import { pushEEResult, pushEOResult, pushResult } from '../utils';

const saveTestCESchema = z.object({
  resulset: z.array(
    z.object({
      questionId: z.number(),
      resId: z.string(),
    }),
  ),
  serieId: z.string(),
  resultat: z.number(),
});
export type SaveTestCEInput = z.infer<typeof saveTestCESchema>;
export const saveTestCEUseCase = async (data: SaveTestCEInput) => {
  try {
    const parseData = saveTestCESchema.parse(data);
    const authService = new AuthService();
    const user = await authService.getUser();
    const resultat = await pushResult(
      parseData.resulset,
      parseData.resultat,
      user._id,
      user.accessToken,
      parseData.serieId,
      'CE',
    );
    if (!resultat) throw new Error('Error saving test');
    redirect(`/detail/TCF/CE/${resultat._id}`);
  } catch (error) {
    throw error;
  }
};

export const saveTestCOUseCase = async (data: SaveTestCEInput) => {
  try {
    const parseData = saveTestCESchema.parse(data);
    const authService = new AuthService();
    const user = await authService.getUser();
    const resultat = await pushResult(
      parseData.resulset,
      parseData.resultat,
      user._id,
      user.accessToken,
      parseData.serieId,
      'CO',
    );
    if (!resultat) throw new Error('Error saving test');
    redirect(`/detail/TCF/CO/${resultat._id}`);
  } catch (error) {
    throw error;
  }
};

const saveTestEESchema = z.object({
  payload: z.object({
    textOne: z.string(),
    textTwo: z.string(),
    textThree: z.string(),
  }),
  serie: z.string(),
});

type saveTestEEInput = z.infer<typeof saveTestEESchema>;

export const saveTestEEUseCase = async (data: saveTestEEInput) => {
  try {
    const parseData = saveTestEESchema.parse(data);
    const authService = new AuthService();
    const user = await authService.getUser();
    const payload = {
      user: user._id,
      serie: parseData.serie,
      payload: JSON.stringify(parseData.payload),
      resultat: [],
      status: '',
    };
    await pushEEResult(payload, user.accessToken);
  } catch (error) {
    throw error;
  }
};

const saveTestEOSchema = z.object({
  payload: z.object({
    taskUrl1: z.string(),
    taskUrl2: z.string(),
    taskUrl3: z.string(),
  }),
  serie: z.string(),
});

type saveTestEOInput = z.infer<typeof saveTestEOSchema>;

export const saveTestEOUseCase = async (data: saveTestEOInput) => {
  try {
    const parseData = saveTestEOSchema.parse(data);
    const authService = new AuthService();
    const user = await authService.getUser();

    const payload = {
      user: user._id,
      serie: parseData.serie,
      payload: JSON.stringify(parseData.payload),
      resultat: [],
      status: '',
    };
    await pushEOResult(payload, user.accessToken);
  } catch (error) {
    throw error;
  }
};
