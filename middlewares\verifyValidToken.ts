import { getToken } from 'next-auth/jwt';
import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';
import { isPublicPath, isAuthPath } from './helpers';
import logger from '@/lib/logger';

export function verifyValidToken(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    // Skip token verification for public paths and auth paths to prevent redirect loops
    const ispublic = isPublicPath(req.nextUrl.pathname);
    const isauth = isAuthPath(req.nextUrl.pathname);
    
    if (ispublic || isauth) {
      return NextResponse.next();
    }

   
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
    const callBackUrl = `${req.nextUrl.pathname}${req.nextUrl.search}`;

    if (!token) {
      logger.log('Not token', { token });

      // Create response with redirect
      const response = NextResponse.redirect(
        new URL(
          `/auth?callbackUrl=${encodeURIComponent(callBackUrl)}`,
          req.nextUrl,
        ),
      );



      return response;
    }

    const isValidToken = await getUserInfo(token.accessToken);
    if (!isValidToken) {
      logger.log('invalid token');

      // Create response with redirect
      const response = NextResponse.redirect(
        new URL(
          `/auth?callbackUrl=${encodeURIComponent(callBackUrl)}&expired=true`,
          req.nextUrl,
        ),
      );

           response.cookies.set('next-auth.session-token', '', { maxAge: 0 });
  response.cookies.set('__Secure-next-auth.session-token', '', { maxAge: 0 });
  response.cookies.set('next-auth.csrf-token', '', { maxAge: 0 });
  response.cookies.set('__Secure-next-auth.csrf-token', '', { maxAge: 0 });
  response.cookies.set('next-auth.callback-url', '', { maxAge: 0 });
  response.cookies.set('__Secure-next-auth.callback-url', '', { maxAge: 0 });

      return response;
    }

    return middleware(req, event);
  };
}

async function getUserInfo(accessToken: string) {
  try {
    const result = await fetch(
      `${process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || ''}/api/user/users/user-info`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        // Add timeout to prevent hanging requests
      },
    );
    const data = await result.json();
    logger.log('User data:', data);
    return result.ok;
  } catch (error) {
    // Log error but don't redirect to prevent loops
    console.error('Token validation failed:', error);
    // Return true to allow access in case of network issues
    // This prevents redirect loops when the API is down
    return true;
  }
}
