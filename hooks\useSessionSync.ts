'use client';

import { useSession } from 'next-auth/react';
import { useEffect } from 'react';

/**
 * Hook pour synchroniser la session côté client avec les changements de cookies côté serveur
 * Utile quand le middleware supprime les cookies mais que le cache côté client persiste
 */
export function useSessionSync() {
  const { update } = useSession();

  useEffect(() => {
    // Fonction pour vérifier si les cookies NextAuth existent
    const checkAuthCookies = () => {
      const cookies = document.cookie;
      const hasSessionToken = cookies.includes('next-auth.session-token') || 
                             cookies.includes('__Secure-next-auth.session-token');
      return hasSessionToken;
    };

    // Fonction pour forcer la mise à jour de la session
    const syncSession = async () => {
      const hasCookies = checkAuthCookies();
      if (!hasCookies) {
        // Si les cookies ont été supprimés, forcer la mise à jour de la session
        await update();
      }
    };

    // Vérifier périodiquement les cookies
    const interval = setInterval(syncSession, 2000); // Vérifier toutes les 2 secondes

    // Écouter les changements de focus pour vérifier immédiatement
    const handleFocus = () => {
      syncSession();
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, [update]);
}
